<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="bg-[#F8F8F8]">
    <view class="px-30rpx py-30rpx text-28rpx text-[#222]">
      <!-- 表单区域 -->
      <view class="mb-20rpx rounded-20rpx bg-white overflow-hidden">
        <wd-cell-group>
          <!-- 姓名 -->
          <wd-input
            v-model="formData.receiver"
            label="姓名"
            label-width="172rpx"
            placeholder="请输入收货人姓名"
            clearable
            custom-class="py-6rpx"
          />

          <!-- 手机号 -->
          <wd-input
            v-model="formData.phone"
            label="手机号"
            label-width="172rpx"
            placeholder="请输入手机号"
            type="number"
            :maxlength="11"
            clearable
            custom-class="py-6rpx"
          />

          <!-- 所在地区 - 三级联动选择器 -->
          <wd-picker
            v-model="regionValue"
            :columns="regionColumns"
            label="所在地区"
            label-width="172rpx"
            placeholder="请选择所在地区"
            :column-change="onChangeDistrict"
            :display-format="displayFormat"
            @confirm="confirmRegion"
            custom-class="py-6rpx"
          />

          <!-- 详细地址 -->
          <wd-input
            v-model="formData.detail"
            label="详细地址"
            label-width="172rpx"
            placeholder="请输入详细地址，如街道、小区、楼栋号、单元室等"
            clearable
            show-word-limit
            custom-class="py-6rpx"
          />
        </wd-cell-group>
      </view>

      <!-- 设为默认 -->
      <view class="ml-20rpx mt-60rpx flex items-center">
        <wd-checkbox checked-color="var(--primary-color)" v-model="formData.is_default" />
        <text class="text-28rpx text-[#333]">设为默认收货地址</text>
      </view>

      <!-- 保存按钮 -->
      <view class="mt-164rpx">
        <view
          v-if="fromPage === 'order'"
          class="m-(x-auto y-30rpx) h-44rpx w-546rpx rounded-12rpx bg-#000 p-(x-30rpx y-5rpx) text-center text-26rpx text-white leading-44rpx op-60"
        >
          请确认正确收货地址，确认订单后该地址不可修改！
        </view>
        <wd-button class="!btn-primary" :loading="loading" @click="saveAddress">保存</wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { addUserAddressApi, getUserAddressDetailApi, updateUserAddressApi } from '@/api'
import district from '@/static/area.js'
import { Utils } from '@/utils'

// 表单数据
const formData = reactive({
  id: '',
  receiver: '', // 姓名
  phone: '',
  province: '',
  city: '',
  district: '',
  detail: '', // 详细地址
  is_default: 0 // 设为默认 0/1
})

// 加载状态
const loading = ref(false)

/**
 * 地区数据工具函数
 * 使用完整的省市区数据，支持全国所有省市区的三级联动
 */

/**
 * 获取省份列表
 * @returns 省份数组
 */
const getProvinceList = () => {
  return district[0] || []
}

/**
 * 根据省份代码获取城市列表
 * @param provinceCode 省份代码
 * @returns 城市数组
 */
const getCityList = (provinceCode: string) => {
  return district[provinceCode] || []
}

/**
 * 根据城市代码获取区县列表
 * @param cityCode 城市代码
 * @returns 区县数组
 */
const getDistrictList = (cityCode: string) => {
  return district[cityCode] || []
}

/**
 * 根据区域代码查找区域名称
 * @param code 区域代码
 * @param level 级别：'province' | 'city' | 'district'
 * @returns 区域名称
 */
const getAreaNameByCode = (code: string, level: 'province' | 'city' | 'district') => {
  if (level === 'province') {
    const province = getProvinceList().find(item => item.value === code)
    return province?.label || ''
  }

  // 对于市级和区级，需要遍历查找
  for (const key in district) {
    if (key !== '0') {
      const areas = district[key]
      const area = areas.find(item => item.value === code)
      if (area) {
        return area.label
      }
    }
  }
  return ''
}

// 选择器默认值 - [省, 市, 区]
const regionValue = ref([])

/**
 * 初始化三级联动的列数据
 * 第一列：省份列表
 * 第二列：根据第一列第一项决定的市级列表
 * 第三列：根据第二列第一项决定的区级列表
 */
const initRegionColumns = () => {
  const provinces = getProvinceList()
  if (provinces.length === 0) return [[], [], []]

  const firstProvince = provinces[0]
  const cities = getCityList(firstProvince.value)
  if (cities.length === 0) return [provinces, [], []]

  const firstCity = cities[0]
  const districts = getDistrictList(firstCity.value)

  // 设置默认选中值
  if (!regionValue.value.length) {
    regionValue.value = [firstProvince.value, firstCity.value, districts[0]?.value || '']
  }

  return [provinces, cities, districts]
}

const regionColumns = ref(initRegionColumns())

/**
 * 处理地区选择器的列变化事件
 * @param pickerView 选择器实例，用于设置列数据
 * @param value 当前选择的所有列的值
 * @param columnIndex 当前变化的列索引
 * @param resolve 完成回调函数
 */
const onChangeDistrict = (pickerView, value, columnIndex, resolve) => {
  const item = value[columnIndex]

  // 如果是省份列变化
  if (columnIndex === 0) {
    const cities = getCityList(item.value)
    // 更新市级列表
    pickerView.setColumnData(1, cities)

    // 更新区级列表，取第一个市的下级区
    if (cities.length > 0) {
      const districts = getDistrictList(cities[0].value)
      pickerView.setColumnData(2, districts)
    } else {
      pickerView.setColumnData(2, [])
    }
  }
  // 如果是市级列变化
  else if (columnIndex === 1) {
    const districts = getDistrictList(item.value)
    // 更新区级列表
    pickerView.setColumnData(2, districts)
  }

  resolve() // 完成列变化处理
}

/**
 * 显示格式化函数，将选中的项转为显示文本
 * @param items 选中的项
 * @return 格式化后的显示文本
 */
const displayFormat = items => {
  return items
    .map(item => {
      return item.label
    })
    .join(' ')
}

/**
 * 确认地区选择事件处理函数
 * @param event 事件对象，包含selectedItems和value
 */
const confirmRegion = (event: { selectedItems: { label: string; value: string }[]; value: string[] }) => {
  console.log(`地区选择事件 -->`, event)
  const items = event?.selectedItems || []

  // 确保选择了完整的三级地区
  if (Array.isArray(items) && items.length >= 3) {
    formData.province = items[0]?.label || ''
    formData.city = items[1]?.label || ''
    formData.district = items[2]?.label || ''

    console.log(`已选择地区: ${formData.province} ${formData.city} ${formData.district}`)
  } else {
    // 如果选择不完整，清空地区信息
    formData.province = ''
    formData.city = ''
    formData.district = ''
    console.warn('地区选择不完整，已清空地区信息')
  }

  // 更新选择器的值
  regionValue.value = event.value || []
}

/**
 * 根据省市区名称设置选择器的值
 * 用于编辑地址时回显地区信息
 */
const setRegionByNames = (provinceName: string, cityName: string, districtName: string) => {
  if (!provinceName || !cityName || !districtName) return

  // 查找省份
  const provinces = getProvinceList()
  const province = provinces.find(p => p.label === provinceName)
  if (!province) return

  // 查找城市
  const cities = getCityList(province.value)
  const city = cities.find(c => c.label === cityName)
  if (!city) return

  // 查找区县
  const districts = getDistrictList(city.value)
  const district = districts.find(d => d.label === districtName)
  if (!district) return

  // 设置选择器的值和列数据
  regionValue.value = [province.value, city.value, district.value]
  regionColumns.value = [provinces, cities, districts]

  console.log(`地区回显成功: ${provinceName} ${cityName} ${districtName}`)
}

// 切换默认地址状态
const toggleDefault = () => {
  formData.is_default = formData.is_default ? 0 : 1
}

/**
 * 保存地址信息
 * 验证表单、生成完整地址并提交数据
 */
const saveAddress = async () => {
  if (formData.receiver.trim() === '') {
    uni.showToast({ title: '请输入收货人姓名', icon: 'none' })
    return
  }
  if (!/^1\d{10}$/.test(formData.phone)) {
    uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
    return
  }
  if (formData.province === '' || formData.city === '' || formData.district === '') {
    uni.showToast({ title: '请选择所在地区', icon: 'none' })
    return
  }
  if (formData.detail.trim() === '') {
    uni.showToast({ title: '请输入详细地址', icon: 'none' })
    return
  }

  loading.value = true

  console.log(`formData -->`, formData)

  formData.is_default = formData.is_default ? 1 : 0

  let api
  if (formData.id) {
    api = updateUserAddressApi
  } else {
    delete formData.id
    api = addUserAddressApi
  }

  let [res, err] = await api(formData)
  if (err) {
    loading.value = false
    Utils.toast({ type: 'error', msg: err.data.message })
    return
  }
  Utils.toast({ type: 'success', msg: res.message || '地址添加成功' })
  uni.$emit('refreshAddressList') // 新增：通知列表页刷新
  uni.navigateBack()
  loading.value = false
}

const fromPage = ref('')
const editAddressInfo = ref({})

// 页面加载
onLoad((options: any) => {
  // 有id证明是编辑页面
  if (options.id) {
    // 获取编辑地址信息详情
    getUserAddressDetailApi(options.id)
      .unwrap()
      .then(res => {
        console.log(`获取地址详情 -->`, res?.data.address)
        const addressData = res.data.address

        // 填充表单数据
        Object.keys(formData).forEach(key => {
          if (addressData.hasOwnProperty(key)) {
            formData[key] = addressData[key]
          }
        })

        // 如果有省市区信息，设置选择器回显
        if (addressData.province && addressData.city && addressData.district) {
          setRegionByNames(addressData.province, addressData.city, addressData.district)
        }

        console.log(`表单数据已填充 -->`, formData)
      })
      .catch(err => {
        console.error('获取地址详情失败:', err)
        Utils.toast({ type: 'error', msg: '获取地址信息失败' })
      })
  }

  if (options.from) {
    fromPage.value = options.from
  }
})

onMounted(() => {
  if (fromPage.value === 'edit') {
    uni.setNavigationBarTitle({
      title: '编辑地址'
    })
  } else {
    uni.setNavigationBarTitle({
      title: '新增地址'
    })
  }
})
</script>

<style>
.region-value {
  font-size: 28rpx;
  color: #222;
}

.region-placeholder {
  font-size: 28rpx;
  color: #999;
}
</style>
